import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Play, Square, RotateCcw, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';
import { AutoGenerateForm } from '@/components/AutoGenerateForm';
import { SchedulePreview } from '@/components/SchedulePreview';
import { ScheduleGenerator, GenerationProgress, GenerationResult } from '@/lib/scheduleGenerator';
import { AutoGenerationParameters, TimeSlot } from '@/lib/schemas';
import { ScheduleConflict } from '@/lib/constraints';
import { notify } from '@/lib/notifications';

type GenerationPhase = 'setup' | 'generating' | 'preview' | 'completed';

const AutoGenerate: React.FC = () => {
  const navigate = useNavigate();
  const {
    teachers,
    classrooms,
    levels,
    timeSlots,
    availableTimeSlots,
    setTimeSlots
  } = useStore();

  const [phase, setPhase] = useState<GenerationPhase>('setup');
  const [parameters, setParameters] = useState<AutoGenerationParameters | null>(null);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress | null>(null);
  const [generationResult, setGenerationResult] = useState<GenerationResult | null>(null);
  const [generatedTimeSlots, setGeneratedTimeSlots] = useState<TimeSlot[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleStartGeneration = async (params: AutoGenerationParameters) => {
    setParameters(params);
    setPhase('generating');
    setIsGenerating(true);
    setGenerationProgress(null);
    setGenerationResult(null);

    try {
      const generator = new ScheduleGenerator(
        teachers,
        classrooms,
        levels,
        availableTimeSlots
      );

      const result = await generator.generateSchedule(
        params,
        timeSlots,
        (progress: GenerationProgress) => {
          setGenerationProgress(progress);
        }
      );

      setGenerationResult(result);
      setGeneratedTimeSlots(result.timeSlots);
      
      if (result.success) {
        setPhase('preview');
        notify.success('Schedule generated successfully!');
      } else {
        notify.error(`Generation failed: ${result.message}`);
        setPhase('setup');
      }
    } catch (error) {
      console.error('Generation error:', error);
      notify.error('An unexpected error occurred during generation');
      setPhase('setup');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleApplySchedule = () => {
    if (generatedTimeSlots.length > 0) {
      setTimeSlots(generatedTimeSlots);
      setPhase('completed');
      notify.success('Schedule applied successfully!');

      // Navigate back to main view after a short delay
      setTimeout(() => {
        navigate('/');
      }, 2000);
    }
  };

  const handleRestart = () => {
    setPhase('setup');
    setParameters(null);
    setGenerationProgress(null);
    setGenerationResult(null);
    setGeneratedTimeSlots([]);
    setIsGenerating(false);
  };

  const getPhaseIcon = () => {
    switch (phase) {
      case 'setup':
        return <Play className="h-5 w-5" />;
      case 'generating':
        return <Square className="h-5 w-5" />;
      case 'preview':
        return <CheckCircle className="h-5 w-5" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Play className="h-5 w-5" />;
    }
  };

  const getConflictIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/')}
              className="glass-button"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Schedule
            </Button>
            
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Auto Generate Schedule
              </h1>
              <p className="text-muted-foreground">
                Automatically generate an optimized timetable using AI algorithms
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {getPhaseIcon()}
            <Badge variant="outline" className="capitalize">
              {phase}
            </Badge>
          </div>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Generation Progress</span>
            <span className="text-sm text-muted-foreground">
              {generationProgress?.progress || 0}%
            </span>
          </div>
          <Progress 
            value={generationProgress?.progress || 0} 
            className="h-2"
          />
          {generationProgress && (
            <p className="text-xs text-muted-foreground mt-1">
              {generationProgress.message}
            </p>
          )}
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Form/Controls */}
          <div className="lg:col-span-1">
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>Generation Settings</span>
                </CardTitle>
                <CardDescription>
                  Configure parameters for schedule generation
                </CardDescription>
              </CardHeader>
              <CardContent>
                {phase === 'setup' && (
                  <AutoGenerateForm
                    teachers={teachers}
                    classrooms={classrooms}
                    levels={levels}
                    onGenerate={handleStartGeneration}
                    disabled={isGenerating}
                  />
                )}

                {phase === 'generating' && generationProgress && (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <h3 className="font-semibold mb-2">Generating Schedule</h3>
                      <p className="text-sm text-muted-foreground">
                        {generationProgress.message}
                      </p>
                    </div>

                    {generationProgress.currentGeneration && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Generation:</span>
                          <span>{generationProgress.currentGeneration}</span>
                        </div>
                        {generationProgress.bestFitness && (
                          <div className="flex justify-between text-sm">
                            <span>Best Fitness:</span>
                            <span>{generationProgress.bestFitness.toFixed(1)}</span>
                          </div>
                        )}
                        {generationProgress.estimatedTimeRemaining && (
                          <div className="flex justify-between text-sm">
                            <span>Time Remaining:</span>
                            <span>{Math.ceil(generationProgress.estimatedTimeRemaining / 1000)}s</span>
                          </div>
                        )}
                      </div>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRestart}
                      className="w-full"
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Cancel Generation
                    </Button>
                  </div>
                )}

                {(phase === 'preview' || phase === 'completed') && generationResult && (
                  <div className="space-y-4">
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">Generation Complete</h3>
                      <p className="text-sm text-muted-foreground">
                        {generationResult.message}
                      </p>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Time Slots:</span>
                        <span>{generationResult.timeSlots.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Generations:</span>
                        <span>{generationResult.generationsUsed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Execution Time:</span>
                        <span>{(generationResult.executionTime / 1000).toFixed(1)}s</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Fitness Score:</span>
                        <span>{generationResult.fitness.toFixed(1)}</span>
                      </div>
                    </div>

                    {generationResult.conflicts.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Conflicts ({generationResult.conflicts.length})</h4>
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                          {generationResult.conflicts.slice(0, 5).map((conflict, index) => (
                            <div key={index} className="flex items-start space-x-2 text-xs">
                              {getConflictIcon(conflict.severity)}
                              <span className="flex-1">{conflict.description}</span>
                            </div>
                          ))}
                          {generationResult.conflicts.length > 5 && (
                            <p className="text-xs text-muted-foreground">
                              +{generationResult.conflicts.length - 5} more conflicts
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    <Separator />

                    <div className="space-y-2">
                      {phase === 'preview' && (
                        <Button
                          onClick={handleApplySchedule}
                          className="w-full glass-button bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Apply Schedule
                        </Button>
                      )}
                      
                      <Button
                        variant="outline"
                        onClick={handleRestart}
                        className="w-full"
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Generate New Schedule
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Preview */}
          <div className="lg:col-span-2">
            {(phase === 'preview' || phase === 'completed') && generatedTimeSlots.length > 0 && (
              <SchedulePreview
                timeSlots={generatedTimeSlots}
                teachers={teachers}
                classrooms={classrooms}
                levels={levels}
                conflicts={generationResult?.conflicts || []}
              />
            )}

            {phase === 'setup' && (
              <Card className="glass-card h-full flex items-center justify-center">
                <CardContent className="text-center">
                  <Play className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Ready to Generate</h3>
                  <p className="text-muted-foreground">
                    Configure your generation parameters and click "Generate Schedule" to begin
                  </p>
                </CardContent>
              </Card>
            )}

            {phase === 'generating' && (
              <Card className="glass-card h-full flex items-center justify-center">
                <CardContent className="text-center">
                  <div className="animate-pulse">
                    <div className="h-16 w-16 bg-blue-200 rounded-full mx-auto mb-4"></div>
                    <div className="h-4 bg-blue-200 rounded w-48 mx-auto mb-2"></div>
                    <div className="h-4 bg-blue-200 rounded w-32 mx-auto"></div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoGenerate;
