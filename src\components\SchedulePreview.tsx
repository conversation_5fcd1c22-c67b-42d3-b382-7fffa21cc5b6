import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { TimeSlot, Teacher, Classroom, Level } from '@/lib/schemas';
import { ScheduleConflict } from '@/lib/constraints';

interface SchedulePreviewProps {
  timeSlots: TimeSlot[];
  teachers: Teacher[];
  classrooms: Classroom[];
  levels: Level[];
  conflicts: ScheduleConflict[];
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const SchedulePreview: React.FC<SchedulePreviewProps> = ({
  timeSlots,
  teachers,
  classrooms,
  levels,
  conflicts
}) => {
  const [selectedView, setSelectedView] = useState<'grid' | 'list'>('grid');
  const [showConflicts, setShowConflicts] = useState(true);
  const [selectedDay, setSelectedDay] = useState<number | null>(null);

  const getTeacher = (id: string) => teachers.find(t => t.id === id);
  const getClassroom = (id: string) => classrooms.find(c => c.id === id);
  const getLevel = (id: string) => levels.find(l => l.id === id);

  const getConflictIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getConflictSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 border-red-300 text-red-800';
      case 'high':
        return 'bg-orange-100 border-orange-300 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'low':
        return 'bg-blue-100 border-blue-300 text-blue-800';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getSlotsForDay = (day: number) => {
    return timeSlots
      .filter(slot => slot.dayOfWeek === day)
      .sort((a, b) => a.start.localeCompare(b.start));
  };

  const getUniqueTimeSlots = () => {
    const times = new Set<string>();
    timeSlots.forEach(slot => {
      times.add(slot.start);
    });
    return Array.from(times).sort();
  };

  const getSlotsForCell = (day: number, time: string) => {
    return timeSlots.filter(slot => 
      slot.dayOfWeek === day && slot.start === time
    );
  };

  const isSlotConflicted = (slotId: string) => {
    return conflicts.some(conflict => conflict.affectedSlots.includes(slotId));
  };

  const getSlotConflicts = (slotId: string) => {
    return conflicts.filter(conflict => conflict.affectedSlots.includes(slotId));
  };

  const renderTimeSlot = (slot: TimeSlot, isConflicted: boolean = false) => {
    const teacher = getTeacher(slot.teacherId);
    const classroom = getClassroom(slot.classroomId);
    const level = getLevel(slot.levelId);
    const slotConflicts = getSlotConflicts(slot.id);

    return (
      <motion.div
        key={slot.id}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`
          p-3 rounded-lg border-2 relative group cursor-pointer transition-all
          ${isConflicted && showConflicts ? 'border-red-300 bg-red-50' : 'border-gray-200 bg-white'}
          hover:shadow-md
        `}
        style={{
          borderColor: isConflicted && showConflicts ? '#fca5a5' : teacher?.color,
          backgroundColor: isConflicted && showConflicts ? '#fef2f2' : `${teacher?.color}10`
        }}
      >
        {/* Conflict indicator */}
        {isConflicted && showConflicts && (
          <div className="absolute top-1 right-1">
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </div>
        )}

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Badge 
              variant="outline" 
              style={{ 
                backgroundColor: `${level?.color}20`, 
                borderColor: level?.color,
                color: level?.color 
              }}
              className="text-xs"
            >
              {level?.label}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {slot.start} - {slot.end}
            </span>
          </div>

          <div className="space-y-1">
            <div className="flex items-center space-x-1 text-xs">
              <Users className="h-3 w-3" />
              <span className="truncate">{teacher?.name}</span>
            </div>
            <div className="flex items-center space-x-1 text-xs">
              <MapPin className="h-3 w-3" />
              <span className="truncate">{classroom?.name}</span>
            </div>
          </div>

          {/* Conflict details on hover */}
          {isConflicted && showConflicts && slotConflicts.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-2 p-2 bg-white border border-red-300 rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity z-10">
              <div className="text-xs space-y-1">
                <div className="font-medium text-red-600">Conflicts:</div>
                {slotConflicts.slice(0, 2).map((conflict, index) => (
                  <div key={index} className="flex items-start space-x-1">
                    {getConflictIcon(conflict.severity)}
                    <span className="text-red-700">{conflict.description}</span>
                  </div>
                ))}
                {slotConflicts.length > 2 && (
                  <div className="text-red-600">+{slotConflicts.length - 2} more</div>
                )}
              </div>
            </div>
          )}
        </div>
      </motion.div>
    );
  };

  const renderGridView = () => {
    const uniqueTimes = getUniqueTimeSlots();
    const workingDays = [0, 1, 2, 3, 4]; // Monday to Friday (0-based indexing)

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Weekly Schedule Grid</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowConflicts(!showConflicts)}
            className="text-xs"
          >
            {showConflicts ? <EyeOff className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
            {showConflicts ? 'Hide' : 'Show'} Conflicts
          </Button>
        </div>

        <div className="grid grid-cols-6 gap-2 text-xs">
          {/* Header row */}
          <div className="font-medium text-center p-2">Time</div>
          {workingDays.map(day => (
            <div key={day} className="font-medium text-center p-2">
              {DAYS[day]}
            </div>
          ))}

          {/* Time slots */}
          {uniqueTimes.map(time => (
            <React.Fragment key={time}>
              <div className="text-center p-2 bg-gray-50 rounded font-medium">
                {time}
              </div>
              {workingDays.map(day => {
                const slots = getSlotsForCell(day, time);
                return (
                  <div key={`${day}-${time}`} className="min-h-[80px] p-1">
                    <AnimatePresence>
                      {slots.map(slot => {
                        const isConflicted = isSlotConflicted(slot.id);
                        return (
                          <div key={slot.id} className="mb-1">
                            {renderTimeSlot(slot, isConflicted)}
                          </div>
                        );
                      })}
                    </AnimatePresence>
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  };

  const renderListView = () => {
    const workingDays = [0, 1, 2, 3, 4]; // Monday to Friday (0-based indexing)

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Schedule by Day</h3>
        
        <Tabs value={selectedDay?.toString() || '0'} onValueChange={(value) => setSelectedDay(parseInt(value))}>
          <TabsList className="grid w-full grid-cols-5">
            {workingDays.map(day => (
              <TabsTrigger key={day} value={day.toString()} className="text-xs">
                {DAYS[day].slice(0, 3)}
              </TabsTrigger>
            ))}
          </TabsList>

          {workingDays.map(day => (
            <TabsContent key={day} value={day.toString()}>
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">{DAYS[day]}</CardTitle>
                  <CardDescription className="text-sm">
                    {getSlotsForDay(day).length} classes scheduled
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {getSlotsForDay(day).map(slot => {
                        const isConflicted = isSlotConflicted(slot.id);
                        return renderTimeSlot(slot, isConflicted);
                      })}
                      {getSlotsForDay(day).length === 0 && (
                        <div className="text-center text-muted-foreground py-8">
                          No classes scheduled for this day
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    );
  };

  const renderConflictsSummary = () => {
    const conflictsBySeverity = {
      critical: conflicts.filter(c => c.severity === 'critical'),
      high: conflicts.filter(c => c.severity === 'high'),
      medium: conflicts.filter(c => c.severity === 'medium'),
      low: conflicts.filter(c => c.severity === 'low')
    };

    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Conflicts Summary</span>
          </CardTitle>
          <CardDescription className="text-sm">
            {conflicts.length} total conflicts found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {conflicts.length === 0 ? (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">No conflicts detected!</span>
            </div>
          ) : (
            <div className="space-y-3">
              {Object.entries(conflictsBySeverity).map(([severity, severityConflicts]) => (
                severityConflicts.length > 0 && (
                  <div key={severity}>
                    <div className="flex items-center space-x-2 mb-2">
                      {getConflictIcon(severity)}
                      <span className="text-sm font-medium capitalize">{severity}</span>
                      <Badge variant="outline" className="text-xs">
                        {severityConflicts.length}
                      </Badge>
                    </div>
                    <ScrollArea className="h-24">
                      <div className="space-y-1">
                        {severityConflicts.slice(0, 3).map((conflict, index) => (
                          <Alert key={index} className={`py-2 ${getConflictSeverityColor(conflict.severity)}`}>
                            <AlertDescription className="text-xs">
                              {conflict.description}
                            </AlertDescription>
                          </Alert>
                        ))}
                        {severityConflicts.length > 3 && (
                          <div className="text-xs text-muted-foreground text-center">
                            +{severityConflicts.length - 3} more {severity} conflicts
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                )
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderStatistics = () => {
    const stats = {
      totalSlots: timeSlots.length,
      teachersUsed: new Set(timeSlots.map(s => s.teacherId)).size,
      classroomsUsed: new Set(timeSlots.map(s => s.classroomId)).size,
      levelsScheduled: new Set(timeSlots.map(s => s.levelId)).size,
      avgSlotsPerDay: (timeSlots.length / 5).toFixed(1)
    };

    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Schedule Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span>Total Classes:</span>
              <span className="font-medium">{stats.totalSlots}</span>
            </div>
            <div className="flex justify-between">
              <span>Teachers Used:</span>
              <span className="font-medium">{stats.teachersUsed}/{teachers.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Classrooms Used:</span>
              <span className="font-medium">{stats.classroomsUsed}/{classrooms.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Levels Scheduled:</span>
              <span className="font-medium">{stats.levelsScheduled}/{levels.length}</span>
            </div>
            <div className="flex justify-between col-span-2">
              <span>Avg Classes/Day:</span>
              <span className="font-medium">{stats.avgSlotsPerDay}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <Card className="glass-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Generated Schedule Preview</span>
              </CardTitle>
              <CardDescription>
                Review the generated schedule before applying
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={selectedView === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedView('grid')}
                className="text-xs"
              >
                Grid View
              </Button>
              <Button
                variant={selectedView === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedView('list')}
                className="text-xs"
              >
                List View
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {selectedView === 'grid' ? renderGridView() : renderListView()}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {renderStatistics()}
        {renderConflictsSummary()}
      </div>
    </div>
  );
};
