import { z } from 'zod';
import dotenv from 'dotenv';

// Ensure environment variables are loaded
dotenv.config();

// Environment variables schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('5000'),
  MONGODB_URI: z.string().default('mongodb://localhost:27017/weekly_schedule'),
  MONGODB_TEST_URI: z.string().default('mongodb://localhost:27017/weekly_schedule_test'),
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters').default('weekly_schedule_super_secret_jwt_key_for_development_use_only_32_chars_min'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT_REFRESH_SECRET must be at least 32 characters').default('weekly_schedule_refresh_secret_key_for_development_use_only_32_chars_min'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('30d'),
  FRONTEND_URL: z.string().default('http://localhost:5173'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),
  BCRYPT_SALT_ROUNDS: z.string().transform(Number).default('12'),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
});

// Validate environment variables
const parseEnv = () => {
  try {
    // Debug: Log what we're actually getting from process.env
    console.log('🔍 JWT_SECRET from env:', process.env.JWT_SECRET ? 'Found' : 'Missing');
    console.log('🔍 JWT_REFRESH_SECRET from env:', process.env.JWT_REFRESH_SECRET ? 'Found' : 'Missing');

    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('❌ Invalid environment variables:');
      error.errors.forEach((err) => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
    }
    process.exit(1);
  }
};

const env = parseEnv();

export const config = {
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  mongodbUri: env.NODE_ENV === 'test' ? env.MONGODB_TEST_URI : env.MONGODB_URI,
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN,
    refreshSecret: env.JWT_REFRESH_SECRET,
    refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
  },
  frontendUrl: env.FRONTEND_URL,
  rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
  rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  bcryptSaltRounds: env.BCRYPT_SALT_ROUNDS,
  logLevel: env.LOG_LEVEL,
} as const;

export type Config = typeof config;
