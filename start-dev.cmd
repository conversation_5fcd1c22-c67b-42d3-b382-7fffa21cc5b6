@echo off
echo Starting Weekly Schedule Application...
echo.

REM Check if Node.js is installed
echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
echo Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not installed or not in PATH
    pause
    exit /b 1
)

echo Node.js and npm are available
echo.

REM Check if backend directory exists
if not exist "%~dp0backend" (
    echo Error: Backend directory not found at %~dp0backend
    pause
    exit /b 1
)

REM Check if package.json exists in root
if not exist "%~dp0package.json" (
    echo Error: package.json not found in root directory
    pause
    exit /b 1
)

REM Start backend in a new terminal window (stays open)
echo Starting Backend Server...
start "Backend Server" cmd /k "cd /d "%~dp0backend" && npm run dev"

REM Start frontend in a new terminal window (stays open)
echo Starting Frontend Server...
start "Frontend Server" cmd /k "cd /d "%~dp0" && npm run dev"

REM Wait for servers to start up
echo Waiting 8 seconds for servers to start...
timeout /t 8 /nobreak >nul

REM Open browser to the application
echo Opening browser...
start http://localhost:8080/

echo.
echo Both servers are running in separate terminal windows
echo Application should open in your default browser at: http://localhost:8080/
echo.
echo ========================================
echo NETWORK ACCESS INFORMATION:
echo ========================================
echo Local access: http://localhost:8080/
echo Network access: http://*************:8080/
echo.
echo Other devices on your network can access the app using:
echo http://*************:8080/
echo.
echo Backend API is accessible at:
echo http://*************:5000/api
echo ========================================
echo.
echo You can close this window - the servers will continue running
echo To stop the servers, close the terminal windows or press Ctrl+C in each
pause
