import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useStore } from '@/store/useStore';
import {
  formatTime,
  parseTimeComponents,
  buildTimeString,
  getHourOptions,
  getMinuteOptions,
  getPeriodOptions,
  format12To24,
  format24To12
} from '@/lib/timeUtils';

interface TimePickerProps {
  value?: string;
  onChange: (time: string) => void;
  placeholder?: string;
  className?: string;
}

export const TimePicker: React.FC<TimePickerProps> = ({
  value,
  onChange,
  placeholder = "Select time",
  className
}) => {
  const { timeFormat } = useStore();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedHour, setSelectedHour] = useState<string>('');
  const [selectedMinute, setSelectedMinute] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<string>('AM');

  // Parse existing value when component mounts or value changes
  React.useEffect(() => {
    if (value) {
      // Value is always stored in 24h format internally
      if (timeFormat === '24h') {
        const [hour, minute] = value.split(':');
        setSelectedHour(hour);
        setSelectedMinute(minute);
      } else {
        // Convert 24h to 12h for display
        const time12 = format24To12(value);
        const components = parseTimeComponents(time12, '12h');
        setSelectedHour(components.hour);
        setSelectedMinute(components.minute);
        setSelectedPeriod(components.period || 'AM');
      }
    }
  }, [value, timeFormat]);

  // Generate options based on time format
  const hours = getHourOptions(timeFormat);
  const minutes = getMinuteOptions();
  const periods = getPeriodOptions();

  const handleTimeChange = (hour?: string, minute?: string, period?: string) => {
    const h = hour || selectedHour;
    const m = minute || selectedMinute;
    const p = period || selectedPeriod;

    if (timeFormat === '24h') {
      if (h && m) {
        const timeString = buildTimeString(h, m, undefined, '24h');
        onChange(timeString);
        setIsOpen(false);
      }
    } else {
      if (h && m && p) {
        // Build 12h time string and convert to 24h for storage
        const time12 = buildTimeString(h, m, p, '12h');
        const time24 = format12To24(time12);
        onChange(time24);
        setIsOpen(false);
      }
    }
  };

  const displayValue = value ? formatTime(value, timeFormat) : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !value && "text-muted-foreground",
            className
          )}
        >
          <Clock className="mr-2 h-4 w-4" />
          {displayValue}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-4" align="start">
        <div className="space-y-4">
          <div className="text-sm font-medium">
            Select Time ({timeFormat === '12h' ? '12-hour' : '24-hour'} format)
          </div>
          <div className={`grid gap-2 ${timeFormat === '24h' ? 'grid-cols-2' : 'grid-cols-3'}`}>
            <div className="space-y-2">
              <label className="text-xs font-medium">Hour</label>
              <Select
                value={selectedHour}
                onValueChange={(hour) => {
                  setSelectedHour(hour);
                  handleTimeChange(hour, undefined, undefined);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Hour" />
                </SelectTrigger>
                <SelectContent>
                  {hours.map((hour) => (
                    <SelectItem key={hour} value={hour}>
                      {hour}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-xs font-medium">Minute</label>
              <Select
                value={selectedMinute}
                onValueChange={(minute) => {
                  setSelectedMinute(minute);
                  handleTimeChange(undefined, minute, undefined);
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Min" />
                </SelectTrigger>
                <SelectContent>
                  {minutes.map((minute) => (
                    <SelectItem key={minute} value={minute}>
                      {minute}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {timeFormat === '12h' && (
              <div className="space-y-2">
                <label className="text-xs font-medium">Period</label>
                <Select
                  value={selectedPeriod}
                  onValueChange={(period) => {
                    setSelectedPeriod(period);
                    handleTimeChange(undefined, undefined, period);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="AM/PM" />
                  </SelectTrigger>
                  <SelectContent>
                    {periods.map((period) => (
                      <SelectItem key={period} value={period}>
                        {period}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
