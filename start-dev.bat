@echo off
echo ========================================
echo   Weekly Schedule Development Startup
echo ========================================
echo.

echo Starting MongoDB (if not already running)...
start "MongoDB" cmd /k "mongod --dbpath C:\data\db 2>nul || echo MongoDB may already be running or path not found"

echo.
echo Waiting 3 seconds for MongoDB to start...
timeout /t 3 /nobreak >nul

echo Starting Backend Server...
start "Backend" cmd /k "cd backend && npm run dev"

echo.
echo Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak >nul

echo Starting Frontend Server...
start "Frontend" cmd /k "npm run dev"

echo.
echo Waiting 5 seconds for frontend to start...
timeout /t 5 /nobreak >nul

echo.
echo Opening browser...
start http://localhost:8080

echo.
echo ========================================
echo   Development servers are starting!
echo ========================================
echo.
echo Frontend: http://localhost:8080
echo Backend:  http://localhost:5000
echo.
echo Network access will be shown in the backend terminal.
echo The app will automatically detect the correct API URL.
echo.
echo Press any key to exit this window...
pause >nul
