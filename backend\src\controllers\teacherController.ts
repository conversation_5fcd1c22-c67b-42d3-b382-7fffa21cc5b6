import { Request, Response, NextFunction } from 'express';
import { Teacher } from '@/models/Teacher';
import { TimeSlot } from '@/models/TimeSlot';
import { AppError } from '@/utils/AppError';

export class TeacherController {
  // Create new teacher
  public async createTeacher(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const teacherData = req.body;

      // Convert string IDs to ObjectIds for assigned levels and classrooms
      if (teacherData.assignedLevels) {
        teacherData.assignedLevels = teacherData.assignedLevels.map((id: string) => new mongoose.Types.ObjectId(id));
      }
      if (teacherData.assignedClassrooms) {
        teacherData.assignedClassrooms = teacherData.assignedClassrooms.map((id: string) => new mongoose.Types.ObjectId(id));
      }

      const teacher = new Teacher(teacherData);
      await teacher.save();

      res.status(201).json({
        success: true,
        message: 'Teacher created successfully',
        data: teacher.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all teachers
  public async getTeachers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const teachers = await Teacher.find().sort({ name: 1 });

      res.json({
        success: true,
        message: 'Teachers retrieved successfully',
        data: teachers,
      });
    } catch (error) {
      next(error);
    }
  }

  // Get teacher by ID
  public async getTeacherById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const teacher = await Teacher.findById(id);

      if (!teacher) {
        throw new AppError('Teacher not found', 404);
      }

      res.json({
        success: true,
        message: 'Teacher retrieved successfully',
        data: teacher.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Update teacher
  public async updateTeacher(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const teacher = await Teacher.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!teacher) {
        throw new AppError('Teacher not found', 404);
      }

      res.json({
        success: true,
        message: 'Teacher updated successfully',
        data: teacher.toJSON(),
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete teacher
  public async deleteTeacher(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      // Check if teacher has any time slots
      const timeSlots = await TimeSlot.find({ teacherId: id });
      if (timeSlots.length > 0) {
        throw new AppError('Cannot delete teacher with existing time slots', 400);
      }

      const teacher = await Teacher.findByIdAndDelete(id);

      if (!teacher) {
        throw new AppError('Teacher not found', 404);
      }

      res.json({
        success: true,
        message: 'Teacher deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
