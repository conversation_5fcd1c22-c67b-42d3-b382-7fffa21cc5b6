import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { Teacher, Classroom, Level } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useConfirmDialog } from './ConfirmDialog';
import { notify } from '@/lib/notifications';
import { Edit, RefreshCw, Save } from 'lucide-react';

interface EditEntityModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: 'teacher' | 'classroom' | 'level';
  entityData: Teacher | Classroom | Level | null;
}

export const EditEntityModal: React.FC<EditEntityModalProps> = ({ 
  isOpen, 
  onClose, 
  entityType, 
  entityData 
}) => {
  const { 
    updateTeacherAsync, 
    updateClassroomAsync, 
    updateLevelAsync,
    loadScheduleData 
  } = useStore();
  
  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  const [formData, setFormData] = useState({
    name: '',
    color: '#4F46E5',
    capacity: 20,
  });

  const [isLoading, setIsLoading] = useState(false);

  // Initialize form data when modal opens or entity changes
  useEffect(() => {
    if (entityData) {
      if (entityType === 'teacher') {
        const teacher = entityData as Teacher;
        setFormData({
          name: teacher.name,
          color: teacher.color,
          capacity: 20, // Not used for teachers
        });
      } else if (entityType === 'classroom') {
        const classroom = entityData as Classroom;
        setFormData({
          name: classroom.name,
          color: '#4F46E5', // Not used for classrooms
          capacity: classroom.capacity,
        });
      } else if (entityType === 'level') {
        const level = entityData as Level;
        setFormData({
          name: level.label,
          color: level.color,
          capacity: 20, // Not used for levels
        });
      }
    }
  }, [entityData, entityType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      notify.warning('Please fill in all required fields');
      return;
    }

    if (!entityData) {
      notify.error('No entity data provided');
      return;
    }

    setIsLoading(true);

    try {
      switch (entityType) {
        case 'teacher':
          await updateTeacherAsync(entityData.id, {
            name: formData.name,
            color: formData.color,
          });
          break;

        case 'classroom':
          await updateClassroomAsync(entityData.id, {
            name: formData.name,
            capacity: formData.capacity,
          });
          break;

        case 'level':
          await updateLevelAsync(entityData.id, {
            label: formData.name,
            color: formData.color,
          });
          break;
      }

      // Show confirmation dialog asking if user wants to refresh the schedule grid
      showConfirm({
        title: 'Update Successful',
        description: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} updated successfully! Would you like to refresh the schedule grid to see the updated information, or keep the current view?`,
        confirmText: 'Refresh Schedule Grid',
        cancelText: 'Keep Current View',
        variant: 'default',
        icon: <RefreshCw className="h-6 w-6 text-blue-500" />,
        onConfirm: async () => {
          try {
            await loadScheduleData();
            notify.success('Schedule grid refreshed');
          } catch (error) {
            notify.error('Failed to refresh schedule grid');
          }
        }
      });

      onClose();
    } catch (error) {
      // Error handling is done by the async functions
      console.error('Error updating entity:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getEntityDisplayName = () => {
    switch (entityType) {
      case 'teacher': return 'Teacher';
      case 'classroom': return 'Classroom';
      case 'level': return 'Level';
      default: return 'Entity';
    }
  };

  const getNameFieldLabel = () => {
    return entityType === 'level' ? 'Label' : 'Name';
  };

  const getNameFieldPlaceholder = () => {
    switch (entityType) {
      case 'teacher': return 'Enter teacher name';
      case 'classroom': return 'Enter classroom name';
      case 'level': return 'Enter level label';
      default: return 'Enter name';
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="glass-card border-white/20 max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Edit className="h-5 w-5" />
              <span>Edit {getEntityDisplayName()}</span>
            </DialogTitle>
          </DialogHeader>

          <motion.form
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            onSubmit={handleSubmit}
            className="space-y-4"
          >
            <div className="space-y-2">
              <Label htmlFor="name">
                {getNameFieldLabel()}
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder={getNameFieldPlaceholder()}
                className="glass-button"
                required
                disabled={isLoading}
              />
            </div>

            {(entityType === 'teacher' || entityType === 'level') && (
              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="color"
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                    className="w-16 h-10 glass-button p-1"
                    disabled={isLoading}
                  />
                  <Input
                    value={formData.color}
                    onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                    placeholder="#4F46E5"
                    className="glass-button"
                    disabled={isLoading}
                  />
                </div>
              </div>
            )}

            {entityType === 'classroom' && (
              <div className="space-y-2">
                <Label htmlFor="capacity">Capacity</Label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  max="1000"
                  value={formData.capacity}
                  onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) || 1 })}
                  className="glass-button"
                  required
                  disabled={isLoading}
                />
              </div>
            )}

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="border-gray-300 hover:bg-gray-100 hover:text-gray-900 font-medium"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-lg"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update {getEntityDisplayName()}
                  </>
                )}
              </Button>
            </div>
          </motion.form>
        </DialogContent>
      </Dialog>

      <ConfirmDialog />
    </>
  );
};
