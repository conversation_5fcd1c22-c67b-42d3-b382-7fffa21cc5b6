@echo off
REM ───────────────────────────────────────────────────────────────────────────
REM Make sure we’re in the script’s folder
cd /d "%~dp0"

REM ───────────────────────────────────────────────────────────────────────────
REM 1) Root terminal: run npm run dev
start "Root – npm run dev" cmd /k "npm run dev"

REM ───────────────────────────────────────────────────────────────────────────
REM 2) Backend terminal: change into backend, then run npm run dev
start "Backend – npm run dev" cmd /k "cd backend && npm run dev"

REM ───────────────────────────────────────────────────────────────────────────
REM 3) Open default browser at localhost:8080
start "" "http://localhost:8080/"
