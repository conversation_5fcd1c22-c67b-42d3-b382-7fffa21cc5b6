/**
 * Time formatting utilities for consistent time display across the application
 */

export type TimeFormat = '12h' | '24h';

/**
 * Convert 24-hour time string to 12-hour format
 * @param time24 - Time in 24-hour format (e.g., "14:30")
 * @returns Time in 12-hour format (e.g., "2:30 PM")
 */
export function format24To12(time24: string): string {
  const [hours, minutes] = time24.split(':').map(Number);
  
  if (hours === 0) {
    return `12:${minutes.toString().padStart(2, '0')} AM`;
  } else if (hours < 12) {
    return `${hours}:${minutes.toString().padStart(2, '0')} AM`;
  } else if (hours === 12) {
    return `12:${minutes.toString().padStart(2, '0')} PM`;
  } else {
    return `${hours - 12}:${minutes.toString().padStart(2, '0')} PM`;
  }
}

/**
 * Convert 12-hour time string to 24-hour format
 * @param time12 - Time in 12-hour format (e.g., "2:30 PM")
 * @returns Time in 24-hour format (e.g., "14:30")
 */
export function format12To24(time12: string): string {
  const [time, period] = time12.split(' ');
  const [hours, minutes] = time.split(':').map(Number);
  
  let hour24 = hours;
  if (period === 'AM' && hours === 12) {
    hour24 = 0;
  } else if (period === 'PM' && hours !== 12) {
    hour24 = hours + 12;
  }
  
  return `${hour24.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

/**
 * Format time according to the specified format
 * @param time24 - Time in 24-hour format (internal storage format)
 * @param format - Desired output format
 * @returns Formatted time string
 */
export function formatTime(time24: string, format: TimeFormat): string {
  if (format === '24h') {
    return time24;
  } else {
    return format24To12(time24);
  }
}

/**
 * Parse time input and convert to 24-hour format for storage
 * @param timeInput - Time input (can be 12h or 24h format)
 * @param inputFormat - Format of the input
 * @returns Time in 24-hour format for storage
 */
export function parseTimeInput(timeInput: string, inputFormat: TimeFormat): string {
  if (inputFormat === '24h') {
    return timeInput;
  } else {
    return format12To24(timeInput);
  }
}

/**
 * Generate hour options for time picker based on format
 * @param format - Time format
 * @returns Array of hour options
 */
export function getHourOptions(format: TimeFormat): string[] {
  if (format === '24h') {
    return Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  } else {
    return Array.from({ length: 12 }, (_, i) => (i + 1).toString());
  }
}

/**
 * Generate minute options for time picker
 * @returns Array of minute options (00, 05, 10, ..., 55)
 */
export function getMinuteOptions(): string[] {
  return Array.from({ length: 12 }, (_, i) => (i * 5).toString().padStart(2, '0'));
}

/**
 * Get period options for 12-hour format
 * @returns Array of period options
 */
export function getPeriodOptions(): string[] {
  return ['AM', 'PM'];
}

/**
 * Validate time string format
 * @param time - Time string to validate
 * @param format - Expected format
 * @returns True if valid, false otherwise
 */
export function isValidTimeFormat(time: string, format: TimeFormat): boolean {
  if (format === '24h') {
    return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time);
  } else {
    return /^(1[0-2]|[1-9]):[0-5][0-9] (AM|PM)$/.test(time);
  }
}

/**
 * Parse time components from time string
 * @param timeString - Time string in any supported format
 * @param format - Format of the input string
 * @returns Object with hour, minute, and period (if applicable)
 */
export function parseTimeComponents(timeString: string, format: TimeFormat): {
  hour: string;
  minute: string;
  period?: string;
} {
  if (format === '24h') {
    const [hour, minute] = timeString.split(':');
    return { hour, minute };
  } else {
    const [time, period] = timeString.split(' ');
    const [hour, minute] = time.split(':');
    return { hour, minute, period };
  }
}

/**
 * Build time string from components
 * @param hour - Hour component
 * @param minute - Minute component
 * @param period - Period component (for 12h format)
 * @param format - Target format
 * @returns Formatted time string
 */
export function buildTimeString(
  hour: string, 
  minute: string, 
  period: string | undefined, 
  format: TimeFormat
): string {
  if (format === '24h') {
    return `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
  } else {
    return `${hour}:${minute.padStart(2, '0')} ${period}`;
  }
}
