import React from 'react';
import { useStore } from '@/store/useStore';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Settings } from 'lucide-react';
import { formatTime } from '@/lib/timeUtils';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { timeFormat, setTimeFormat } = useStore();

  const handleTimeFormatChange = (format: '12h' | '24h') => {
    setTimeFormat(format);
  };

  // Example times for preview
  const exampleTime24 = "14:30";
  const exampleTime24_2 = "09:15";

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Application Settings
          </DialogTitle>
          <DialogDescription>
            Configure your preferences for the timetable application.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Time Format
              </CardTitle>
              <CardDescription className="text-sm">
                Choose how times are displayed throughout the application.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="timeFormat">Time Format</Label>
                <Select
                  value={timeFormat}
                  onValueChange={handleTimeFormatChange}
                >
                  <SelectTrigger className="glass-button">
                    <SelectValue placeholder="Select time format" />
                  </SelectTrigger>
                  <SelectContent
                    position="popper"
                    side="bottom"
                    align="start"
                    avoidCollisions={true}
                    collisionPadding={50}
                  >
                    <SelectItem value="12h">
                      <div className="flex flex-col">
                        <span>12-hour (AM/PM)</span>
                        <span className="text-xs text-muted-foreground">
                          American format
                        </span>
                      </div>
                    </SelectItem>
                    <SelectItem value="24h">
                      <div className="flex flex-col">
                        <span>24-hour</span>
                        <span className="text-xs text-muted-foreground">
                          European format
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="p-3 bg-muted/50 rounded-lg">
                <div className="text-sm font-medium mb-2">Preview:</div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Morning class:</span>
                    <span className="font-mono">
                      {formatTime(exampleTime24_2, timeFormat)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Afternoon class:</span>
                    <span className="font-mono">
                      {formatTime(exampleTime24, timeFormat)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="text-xs text-muted-foreground">
                <strong>Note:</strong> This setting affects all time displays including 
                the timetable grid, time pickers, and time slot creation forms. 
                The change will be applied immediately and saved to your preferences.
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
