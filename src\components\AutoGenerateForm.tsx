import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { TimePicker } from '@/components/ui/time-picker';
import { Plus, Minus, Settings, Users, BookOpen, Clock, Target, ChevronDown } from 'lucide-react';
import { 
  AutoGenerationParametersSchema, 
  AutoGenerationParameters, 
  Teacher, 
  Classroom, 
  Level,
  TeacherPreference,
  LevelRequirement
} from '@/lib/schemas';

interface AutoGenerateFormProps {
  teachers: Teacher[];
  classrooms: Classroom[];
  levels: Level[];
  onGenerate: (parameters: AutoGenerationParameters) => void;
  disabled?: boolean;
}

export const AutoGenerateForm: React.FC<AutoGenerateFormProps> = ({
  teachers,
  classrooms,
  levels,
  onGenerate,
  disabled = false
}) => {
  const [teacherPreferences, setTeacherPreferences] = useState<TeacherPreference[]>([]);
  const [levelRequirements, setLevelRequirements] = useState<LevelRequirement[]>([]);
  const [selectedLevelsToAdd, setSelectedLevelsToAdd] = useState<string[]>([]);
  const [isLevelDropdownOpen, setIsLevelDropdownOpen] = useState(false);

  // Clean up invalid preferences when entities are deleted
  useEffect(() => {
    // Remove teacher preferences for deleted teachers
    setTeacherPreferences(prev =>
      prev.filter(pref => teachers.some(teacher => teacher.id === pref.teacherId))
    );
  }, [teachers]);

  useEffect(() => {
    // Remove level requirements for deleted levels
    setLevelRequirements(prev =>
      prev.filter(req => levels.some(level => level.id === req.levelId))
    );
  }, [levels]);

  const form = useForm<AutoGenerationParameters>({
    resolver: zodResolver(AutoGenerationParametersSchema),
    defaultValues: {
      teacherPreferences: [],
      levelRequirements: [],
      constraints: {
        workingHours: {
          startTime: '08:00',
          endTime: '17:00'
        },
        breakPeriods: [
          { name: 'Lunch Break', startTime: '12:00', endTime: '13:00' }
        ],
        minClassDuration: 30,
        maxClassDuration: 120,
        maxConsecutiveClasses: 4,
        workingDays: [0, 1, 2, 3, 4]
      },
      optimizationGoals: {
        minimizeTeacherConflicts: 10,
        maximizeClassroomUtilization: 7,
        balanceWorkloadDistribution: 8,
        minimizeGapsInSchedules: 6,
        respectTeacherPreferences: 9,
        optimizeTimeDistribution: 5
      },
      preserveExistingSlots: false,
      maxGenerationAttempts: 50,
      populationSize: 50,
      maxGenerations: 100
    }
  });

  const addTeacherPreference = () => {
    if (teachers.length === 0) return;
    
    const availableTeachers = teachers.filter(t => 
      !teacherPreferences.find(p => p.teacherId === t.id)
    );
    
    if (availableTeachers.length > 0) {
      const newPreference: TeacherPreference = {
        teacherId: availableTeachers[0].id,
        maxClassesPerDay: 6,
        preferredDays: [1, 2, 3, 4, 5],
        preferredTimeSlots: [],
        minBreakBetweenClasses: 15,
        unavailableSlots: [],
        assignmentMode: 'specific',
        assignedLevels: [],
        assignedClassrooms: []
      };
      setTeacherPreferences([...teacherPreferences, newPreference]);
    }
  };

  const removeTeacherPreference = (index: number) => {
    setTeacherPreferences(teacherPreferences.filter((_, i) => i !== index));
  };

  const updateTeacherPreference = (index: number, updates: Partial<TeacherPreference>) => {
    setTeacherPreferences(teacherPreferences.map((pref, i) => 
      i === index ? { ...pref, ...updates } : pref
    ));
  };

  const addSelectedLevelRequirements = () => {
    if (selectedLevelsToAdd.length === 0) return;

    const newRequirements: LevelRequirement[] = selectedLevelsToAdd.map(levelId => ({
      levelId,
      classesPerWeek: 5,
      preferredTimeDistribution: 'balanced' as const,
      priority: 'medium' as const,
      classDuration: 60,
      requiresSpecialRoom: false
    }));

    setLevelRequirements([...levelRequirements, ...newRequirements]);
    setSelectedLevelsToAdd([]);
    setIsLevelDropdownOpen(false);
  };

  const getAvailableLevels = () => {
    return levels.filter(l =>
      !levelRequirements.find(r => r.levelId === l.id)
    );
  };

  const handleLevelSelection = (levelId: string, checked: boolean) => {
    if (checked) {
      setSelectedLevelsToAdd([...selectedLevelsToAdd, levelId]);
    } else {
      setSelectedLevelsToAdd(selectedLevelsToAdd.filter(id => id !== levelId));
    }
  };

  const removeLevelRequirement = (index: number) => {
    setLevelRequirements(levelRequirements.filter((_, i) => i !== index));
  };

  const updateLevelRequirement = (index: number, updates: Partial<LevelRequirement>) => {
    setLevelRequirements(levelRequirements.map((req, i) => 
      i === index ? { ...req, ...updates } : req
    ));
  };

  const onSubmit = (data: AutoGenerationParameters) => {
    const parameters: AutoGenerationParameters = {
      ...data,
      teacherPreferences,
      levelRequirements
    };
    onGenerate(parameters);
  };

  const getDayName = (day: number) => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[day];
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Tabs defaultValue="requirements" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="requirements" className="text-xs">
              <BookOpen className="h-3 w-3 mr-1" />
              Requirements
            </TabsTrigger>
            <TabsTrigger value="preferences" className="text-xs">
              <Users className="h-3 w-3 mr-1" />
              Teachers
            </TabsTrigger>
            <TabsTrigger value="constraints" className="text-xs">
              <Clock className="h-3 w-3 mr-1" />
              Constraints
            </TabsTrigger>
            <TabsTrigger value="optimization" className="text-xs">
              <Target className="h-3 w-3 mr-1" />
              Goals
            </TabsTrigger>
          </TabsList>

          <TabsContent value="requirements" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Level Requirements</CardTitle>
                <CardDescription className="text-xs">
                  Define how many classes each level needs per week
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <ScrollArea className="h-48 w-full">
                  <div className="space-y-2 pr-4">
                  {levelRequirements.map((req, index) => {
                    const level = levels.find(l => l.id === req.levelId);
                    return (
                      <div key={index} className="flex items-center space-x-2 p-2 border rounded mb-2 min-w-0 overflow-x-auto">
                        <div className="flex items-center space-x-2 min-w-max">
                          <Badge variant="outline" style={{ backgroundColor: `${level?.color}20`, borderColor: level?.color }} className="flex-shrink-0">
                            {level?.label}
                          </Badge>
                          <Input
                            type="number"
                            value={req.classesPerWeek}
                            onChange={(e) => updateLevelRequirement(index, { classesPerWeek: parseInt(e.target.value) || 1 })}
                            className="w-16 h-7 text-xs flex-shrink-0"
                            min="1"
                            max="30"
                          />
                          <span className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">classes/week</span>
                          <Input
                            type="number"
                            value={req.classDuration}
                            onChange={(e) => updateLevelRequirement(index, { classDuration: parseInt(e.target.value) || 60 })}
                            className="w-16 h-7 text-xs flex-shrink-0"
                            min="30"
                            max="180"
                          />
                          <span className="text-xs text-muted-foreground whitespace-nowrap flex-shrink-0">min</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeLevelRequirement(index)}
                            className="h-7 w-7 p-0 flex-shrink-0"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                  </div>
                </ScrollArea>
                <Popover open={isLevelDropdownOpen} onOpenChange={setIsLevelDropdownOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      disabled={getAvailableLevels().length === 0}
                      className="w-full h-8 justify-between"
                    >
                      <span className="flex items-center">
                        <Plus className="h-3 w-3 mr-1" />
                        Add Level Requirements
                      </span>
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-3" align="start">
                    <div className="space-y-3">
                      <div className="text-sm font-medium">Select Levels to Add</div>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {getAvailableLevels().map((level) => (
                          <div key={level.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`level-${level.id}`}
                              checked={selectedLevelsToAdd.includes(level.id)}
                              onCheckedChange={(checked) => handleLevelSelection(level.id, checked as boolean)}
                            />
                            <label
                              htmlFor={`level-${level.id}`}
                              className="flex items-center space-x-2 cursor-pointer flex-1"
                            >
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: level.color }}
                              />
                              <span className="text-sm">{level.label}</span>
                            </label>
                          </div>
                        ))}
                      </div>
                      {getAvailableLevels().length === 0 && (
                        <div className="text-sm text-muted-foreground text-center py-2">
                          All levels have been added
                        </div>
                      )}
                      <div className="flex justify-end space-x-2 pt-2 border-t">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedLevelsToAdd([]);
                            setIsLevelDropdownOpen(false);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          type="button"
                          size="sm"
                          onClick={addSelectedLevelRequirements}
                          disabled={selectedLevelsToAdd.length === 0}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          Add {selectedLevelsToAdd.length > 0 ? `(${selectedLevelsToAdd.length})` : ''}
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Teacher Preferences</CardTitle>
                <CardDescription className="text-xs">
                  Set individual teacher constraints and preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <ScrollArea className="h-48 w-full">
                  <div className="space-y-2 pr-4">
                  {teacherPreferences.map((pref, index) => {
                    const teacher = teachers.find(t => t.id === pref.teacherId);
                    return (
                      <div key={index} className="p-3 border rounded mb-2 space-y-2 min-w-0 overflow-x-auto">
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" style={{ backgroundColor: `${teacher?.color}20`, borderColor: teacher?.color }}>
                            {teacher?.name}
                          </Badge>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTeacherPreference(index)}
                            className="h-6 w-6 p-0"
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <label className="text-xs text-muted-foreground">Max Classes/Day</label>
                            <Input
                              type="number"
                              value={pref.maxClassesPerDay}
                              onChange={(e) => updateTeacherPreference(index, { maxClassesPerDay: parseInt(e.target.value) || 1 })}
                              className="h-7 text-xs"
                              min="1"
                              max="10"
                            />
                          </div>
                          <div>
                            <label className="text-xs text-muted-foreground">Min Break (min)</label>
                            <Input
                              type="number"
                              value={pref.minBreakBetweenClasses}
                              onChange={(e) => updateTeacherPreference(index, { minBreakBetweenClasses: parseInt(e.target.value) || 0 })}
                              className="h-7 text-xs"
                              min="0"
                              max="120"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="text-xs text-muted-foreground">Assignment Mode</label>
                          <Select
                            value={pref.assignmentMode}
                            onValueChange={(value: 'specific' | 'semi-random' | 'fully-random') =>
                              updateTeacherPreference(index, { assignmentMode: value })
                            }
                          >
                            <SelectTrigger className="h-7 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="specific">Specific (Use assigned levels & classrooms)</SelectItem>
                              <SelectItem value="semi-random">Semi-Random (Use assigned levels, random classrooms)</SelectItem>
                              <SelectItem value="fully-random">Fully Random (Random levels & classrooms)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {(pref.assignmentMode === 'specific' || pref.assignmentMode === 'semi-random') && (
                          <div>
                            <label className="text-xs text-muted-foreground">Assigned Levels</label>
                            <div className="grid grid-cols-2 gap-1 max-h-20 overflow-y-auto border rounded p-1">
                              {levels.map((level) => (
                                <div key={level.id} className="flex items-center space-x-1">
                                  <Checkbox
                                    id={`pref-${index}-level-${level.id}`}
                                    checked={pref.assignedLevels?.includes(level.id) || false}
                                    onCheckedChange={(checked) => {
                                      const currentLevels = pref.assignedLevels || [];
                                      if (checked) {
                                        updateTeacherPreference(index, {
                                          assignedLevels: [...currentLevels, level.id]
                                        });
                                      } else {
                                        updateTeacherPreference(index, {
                                          assignedLevels: currentLevels.filter(id => id !== level.id)
                                        });
                                      }
                                    }}
                                  />
                                  <Label
                                    htmlFor={`pref-${index}-level-${level.id}`}
                                    className="text-xs cursor-pointer flex items-center space-x-1"
                                  >
                                    <div
                                      className="w-2 h-2 rounded-full"
                                      style={{ backgroundColor: level.color }}
                                    />
                                    <span>{level.label}</span>
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {pref.assignmentMode === 'specific' && (
                          <div>
                            <label className="text-xs text-muted-foreground">Assigned Classrooms</label>
                            <div className="grid grid-cols-2 gap-1 max-h-20 overflow-y-auto border rounded p-1">
                              {classrooms.map((classroom) => (
                                <div key={classroom.id} className="flex items-center space-x-1">
                                  <Checkbox
                                    id={`pref-${index}-classroom-${classroom.id}`}
                                    checked={pref.assignedClassrooms?.includes(classroom.id) || false}
                                    onCheckedChange={(checked) => {
                                      const currentClassrooms = pref.assignedClassrooms || [];
                                      if (checked) {
                                        updateTeacherPreference(index, {
                                          assignedClassrooms: [...currentClassrooms, classroom.id]
                                        });
                                      } else {
                                        updateTeacherPreference(index, {
                                          assignedClassrooms: currentClassrooms.filter(id => id !== classroom.id)
                                        });
                                      }
                                    }}
                                  />
                                  <Label
                                    htmlFor={`pref-${index}-classroom-${classroom.id}`}
                                    className="text-xs cursor-pointer"
                                  >
                                    {classroom.name}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                  </div>
                </ScrollArea>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTeacherPreference}
                  disabled={teacherPreferences.length >= teachers.length}
                  className="w-full h-8"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Teacher Preference
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="constraints" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Time Constraints</CardTitle>
                <CardDescription className="text-xs">
                  Define working hours and scheduling constraints
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="constraints.workingHours.startTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Start Time</FormLabel>
                        <FormControl>
                          <TimePicker
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select start time"
                            className="h-8 text-xs"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="constraints.workingHours.endTime"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">End Time</FormLabel>
                        <FormControl>
                          <TimePicker
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select end time"
                            className="h-8 text-xs"
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="constraints.minClassDuration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Min Class Duration (min)</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" min="15" max="60" className="h-8 text-xs" />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="constraints.maxClassDuration"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-xs">Max Class Duration (min)</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" min="60" max="240" className="h-8 text-xs" />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="preserveExistingSlots"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-xs">
                          Preserve existing time slots
                        </FormLabel>
                        <FormDescription className="text-xs">
                          Keep current schedule and only add new slots
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="optimization" className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Optimization Goals</CardTitle>
                <CardDescription className="text-xs">
                  Set priorities for different optimization objectives (0-10)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="optimizationGoals.minimizeTeacherConflicts"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel className="text-xs">Minimize Teacher Conflicts</FormLabel>
                        <span className="text-xs text-muted-foreground">{field.value}</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={0}
                          max={10}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="w-full"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="optimizationGoals.balanceWorkloadDistribution"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel className="text-xs">Balance Workload</FormLabel>
                        <span className="text-xs text-muted-foreground">{field.value}</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={0}
                          max={10}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="w-full"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="optimizationGoals.respectTeacherPreferences"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex justify-between">
                        <FormLabel className="text-xs">Respect Teacher Preferences</FormLabel>
                        <span className="text-xs text-muted-foreground">{field.value}</span>
                      </div>
                      <FormControl>
                        <Slider
                          min={0}
                          max={10}
                          step={1}
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="w-full"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Separator />

        <Button
          type="submit"
          disabled={disabled || levelRequirements.length === 0}
          className="w-full glass-button bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30"
        >
          <Settings className="h-4 w-4 mr-2" />
          Generate Schedule
        </Button>
      </form>
    </Form>
  );
};
